cat > /tmp/webtado-complete.conf << 'EOF'
# HTTP to HTTPS redirect
server {
    listen 80;
    server_name webtado.live www.webtado.live;
    
    # Allow Let's Encrypt challenges
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        allow all;
    }
    
    # Redirect all other traffic to HTTPS
    location / {
        return 301 https://www.webtado.live$request_uri;
    }
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name www.webtado.live;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/www.webtado.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.webtado.live/privkey.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # Client max body size for file uploads
    client_max_body_size 100M;
    
    # Static files served by Nginx
    location /static/ {
        alias /var/www/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://app:8000/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        access_log off;
    }
    
    # Main application proxy
    location / {
        proxy_pass http://app:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Logging
    access_log /var/log/nginx/webtado_access.log;
    error_log /var/log/nginx/webtado_error.log;
}

# Redirect non-www to www
server {
    listen 443 ssl http2;
    server_name webtado.live;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/www.webtado.live/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.webtado.live/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    
    # Redirect to www
    return 301 https://www.webtado.live$request_uri;
}
EOF