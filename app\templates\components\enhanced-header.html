<!-- Enhanced Header -->
<style>
.enhanced-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.brand-logo {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 800;
    font-size: 1.5rem;
    letter-spacing: -0.025em;
}

.brand-subtitle {
    color: #64748b;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 2px;
}

.nav-item {
    position: relative;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.875rem;
    color: #475569;
    text-decoration: none;
    transition: all 0.2s ease-in-out;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
    transition: width 0.3s ease;
    z-index: -1;
}

.nav-item:hover::before {
    width: 100%;
}

.nav-item:hover {
    color: #1e40af;
    background: rgba(59, 130, 246, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.nav-item.active {
    color: #1e40af;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-bottom: 2px solid #3b82f6;
}

.user-menu-button {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease-in-out;
    backdrop-filter: blur(5px);
}

.user-menu-button:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: #cbd5e1;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.user-info {
    text-align: right;
}

.user-name {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.875rem;
    line-height: 1.2;
}

.user-role {
    color: #64748b;
    font-size: 0.75rem;
    font-weight: 500;
}

.dropdown-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.dropdown-item {
    padding: 12px 16px;
    color: #475569;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f1f5f9;
}

.dropdown-item:hover {
    background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%);
    color: #1e40af;
    transform: translateX(-2px);
}

.dropdown-item:last-child {
    border-bottom: none;
}

.mobile-toggle {
    padding: 8px;
    border-radius: 8px;
    color: #64748b;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.mobile-toggle:hover {
    background: rgba(255, 255, 255, 0.95);
    color: #1e40af;
    border-color: #cbd5e1;
}

@media (max-width: 768px) {
    .brand-logo {
        font-size: 1.25rem;
    }

    .brand-subtitle {
        font-size: 0.6875rem;
    }
}
</style>

<header class="enhanced-header">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Enhanced Brand -->
            <a href="/" class="flex items-center space-x-3 rtl:space-x-reverse group">
                <div class="text-right">
                    <div class="brand-logo">CMSVS</div>
                    <div class="brand-subtitle">نظام الأرشفة الداخلي</div>
                </div>
            </a>

            <!-- Enhanced Mobile Toggle Button -->
            <button type="button" id="mobile-menu-button" class="md:hidden mobile-toggle focus:outline-none focus:ring-2 focus:ring-blue-500 touch-target" onclick="toggleMobileNav()">
                <span class="sr-only">القائمة</span>
                <svg id="menu-icon" class="h-6 w-6 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
                <svg id="close-icon" class="h-6 w-6 hidden transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex md:items-center md:space-x-6 rtl:space-x-reverse">
                <ul class="flex space-x-6 rtl:space-x-reverse">
                    {% if current_user %}
                    {% if current_user.role.value == 'admin' %}
                    <li>
                        <a href="/admin/dashboard" class="nav-item" data-page="admin-dashboard">
                            لوحة الإدارة
                        </a>
                    </li>
                    <li>
                        <a href="/admin/users" class="nav-item" data-page="admin-users">
                            إدارة المستخدمين
                        </a>
                    </li>
                    <li>
                        <a href="/admin/requests" class="nav-item" data-page="admin-requests">
                            إدارة الطلبات
                        </a>
                    </li>
                    <li>
                        <a href="/admin/requests-records" class="nav-item" data-page="admin-records">
                            سجل الطلبات
                        </a>
                    </li>
                    <li>
                        <a href="/admin/user-activity-report" class="nav-item" data-page="admin-reports">
                            تقارير النشاط
                        </a>
                    </li>
                    {% else %}
                    <li>
                        <a href="/dashboard" class="nav-item" data-page="dashboard">
                            الرئيسية
                        </a>
                    </li>
                    <li>
                        <a href="/requests" class="nav-item" data-page="requests">
                            طلباتي
                        </a>
                    </li>
                    <li>
                        <a href="/requests/new" class="nav-item" data-page="new-request">
                            طلب جديد
                        </a>
                    </li>
                    {% endif %}
                    {% endif %}
                </ul>

                <!-- Enhanced User Menu -->
                {% if current_user %}
                <div class="relative ml-3" x-data="{ open: false }">
                    <button type="button"
                            class="user-menu-button focus:outline-none focus:ring-2 focus:ring-blue-500"
                            @click="open = !open">
                        <div class="user-avatar">
                            {{ current_user.full_name[0] if current_user.full_name else 'م' }}
                        </div>
                        <div class="user-info">
                            <div class="user-name">{{ current_user.full_name or 'مستخدم' }}</div>
                            <div class="user-role">
                                {% if current_user.role.value == 'admin' %}
                                System Administrator
                                {% else %}
                                مستخدم
                                {% endif %}
                            </div>
                        </div>
                        <svg class="h-4 w-4 text-gray-400 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>

                    <ul class="dropdown-menu absolute left-0 mt-3 w-52 py-2 z-50"
                        x-show="open"
                        @click.away="open = false"
                        x-transition:enter="transition ease-out duration-200"
                        x-transition:enter-start="transform opacity-0 scale-95 translate-y-1"
                        x-transition:enter-end="transform opacity-100 scale-100 translate-y-0"
                        x-transition:leave="transition ease-in duration-150"
                        x-transition:leave-start="transform opacity-100 scale-100 translate-y-0"
                        x-transition:leave-end="transform opacity-0 scale-95 translate-y-1">
                        <li>
                            <a href="/profile" class="dropdown-item flex items-center">
                                <i class="fas fa-user text-gray-400 ml-3 w-4"></i>
                                الملف الشخصي
                            </a>
                        </li>
                        <li>
                            <a href="/settings" class="dropdown-item flex items-center">
                                <i class="fas fa-cog text-gray-400 ml-3 w-4"></i>
                                الإعدادات
                            </a>
                        </li>
                        <li>
                            <div class="border-t border-gray-100 my-1"></div>
                        </li>
                        <li>
                            <form method="post" action="/logout" class="block">
                                <button type="submit" class="dropdown-item w-full text-right flex items-center">
                                    <i class="fas fa-sign-out-alt text-gray-400 ml-3 w-4"></i>
                                    تسجيل الخروج
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
                {% else %}
                <!-- Guest User Actions -->
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                    <a href="/login" class="btn-primary">
                        تسجيل الدخول
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t border-gray-200 shadow-lg">
            <div class="px-4 py-3 space-y-1">
                {% if current_user %}
                {% if current_user.role.value == 'admin' %}
                <a href="/admin/dashboard" class="mobile-nav-item">
                    <i class="fas fa-tachometer-alt ml-3"></i>
                    لوحة الإدارة
                </a>
                <a href="/admin/users" class="mobile-nav-item">
                    <i class="fas fa-users ml-3"></i>
                    إدارة المستخدمين
                </a>
                <a href="/admin/requests" class="mobile-nav-item">
                    <i class="fas fa-file-alt ml-3"></i>
                    إدارة الطلبات
                </a>
                <a href="/admin/requests-records" class="mobile-nav-item">
                    <i class="fas fa-history ml-3"></i>
                    سجل الطلبات
                </a>
                <a href="/admin/user-activity-report" class="mobile-nav-item">
                    <i class="fas fa-chart-bar ml-3"></i>
                    تقارير النشاط
                </a>
                {% else %}
                <a href="/dashboard" class="mobile-nav-item">
                    <i class="fas fa-tachometer-alt ml-3"></i>
                    لوحة التحكم
                </a>
                <a href="/requests" class="mobile-nav-item">
                    <i class="fas fa-file-alt ml-3"></i>
                    طلباتي
                </a>
                <a href="/requests/new" class="mobile-nav-item">
                    <i class="fas fa-plus ml-3"></i>
                    طلب جديد
                </a>
                <a href="/achievements" class="mobile-nav-item">
                    <i class="fas fa-trophy ml-3"></i>
                    الإنجازات
                </a>
                <a href="/messages/inbox" class="mobile-nav-item">
                    <i class="fas fa-envelope ml-3"></i>
                    الرسائل
                </a>
                <a href="/profile" class="mobile-nav-item">
                    <i class="fas fa-user ml-3"></i>
                    الملف الشخصي
                </a>
                {% endif %}

                <!-- Mobile User Menu -->
                <div class="border-t border-gray-200 pt-3 mt-3">
                    <div class="flex items-center px-4 py-2">
                        <div class="flex-shrink-0">
                            {% if current_user.avatar_url %}
                            <img class="h-8 w-8 rounded-full" src="{{ current_user.avatar_url }}" alt="{{ current_user.full_name or current_user.username }}">
                            {% else %}
                            <div class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium text-gray-700">{{ (current_user.full_name or current_user.username)[0] }}</span>
                            </div>
                            {% endif %}
                        </div>
                        <div class="mr-3">
                            <div class="text-base font-medium text-gray-800">{{ current_user.full_name or current_user.username }}</div>
                            <div class="text-sm text-gray-500">{{ current_user.email }}</div>
                        </div>
                    </div>
                    <a href="/logout" class="mobile-nav-item text-red-600 hover:bg-red-50">
                        <i class="fas fa-sign-out-alt ml-3"></i>
                        تسجيل الخروج
                    </a>
                </div>
                {% else %}
                <a href="/login" class="mobile-nav-item">
                    <i class="fas fa-sign-in-alt ml-3"></i>
                    تسجيل الدخول
                </a>
                <a href="/register" class="mobile-nav-item">
                    <i class="fas fa-user-plus ml-3"></i>
                    إنشاء حساب
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</header>

<!-- Enhanced Header JavaScript -->
<script>
// Mobile menu toggle function
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    const menuIcon = document.getElementById('menu-icon');
    const closeIcon = document.getElementById('close-icon');

    if (mobileMenu.classList.contains('hidden')) {
        // Show menu
        mobileMenu.classList.remove('hidden');
        menuIcon.classList.add('hidden');
        closeIcon.classList.remove('hidden');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    } else {
        // Hide menu
        mobileMenu.classList.add('hidden');
        menuIcon.classList.remove('hidden');
        closeIcon.classList.add('hidden');
        document.body.style.overflow = ''; // Restore scrolling
    }
}

// Close mobile menu when clicking on a link
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('mobile-nav-item')) {
        const mobileMenu = document.getElementById('mobile-menu');
        const menuIcon = document.getElementById('menu-icon');
        const closeIcon = document.getElementById('close-icon');

        mobileMenu.classList.add('hidden');
        menuIcon.classList.remove('hidden');
        closeIcon.classList.add('hidden');
        document.body.style.overflow = '';
    }
});

// Close mobile menu on window resize to desktop size
window.addEventListener('resize', function() {
    if (window.innerWidth >= 768) { // md breakpoint
        const mobileMenu = document.getElementById('mobile-menu');
        const menuIcon = document.getElementById('menu-icon');
        const closeIcon = document.getElementById('close-icon');

        mobileMenu.classList.add('hidden');
        menuIcon.classList.remove('hidden');
        closeIcon.classList.add('hidden');
        document.body.style.overflow = '';
    }
});

document.addEventListener('DOMContentLoaded', function() {
    // Get current page URL
    const currentPath = window.location.pathname;

    // Get all navigation items
    const navItems = document.querySelectorAll('.nav-item');

    // Remove active class from all items
    navItems.forEach(item => {
        item.classList.remove('active');
    });

    // Add active class to current page item
    navItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href && (currentPath === href || (href !== '/' && currentPath.startsWith(href)))) {
            item.classList.add('active');
        }
    });

    // Special handling for exact matches
    if (currentPath === '/admin/dashboard') {
        document.querySelector('a[href="/admin/dashboard"]')?.classList.add('active');
    } else if (currentPath === '/dashboard') {
        document.querySelector('a[href="/dashboard"]')?.classList.add('active');
    }

    // Add smooth scroll behavior for anchor links
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Add a subtle click animation
            this.style.transform = 'translateY(1px)';
            setTimeout(() => {
                this.style.transform = 'translateY(-1px)';
            }, 100);
        });
    });

    // Enhanced dropdown animation
    const userMenuButton = document.querySelector('.user-menu-button');
    if (userMenuButton) {
        userMenuButton.addEventListener('click', function() {
            const arrow = this.querySelector('svg');
            arrow.style.transform = arrow.style.transform === 'rotate(180deg)' ? 'rotate(0deg)' : 'rotate(180deg)';
        });
    }
});
</script>