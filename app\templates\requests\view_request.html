{% extends "base.html" %}

{% block title %}عرض الطلب {{ req.request_number }} - CMSVS{% endblock %}

{% block content %}
<style>
/* Professional Request View Styling */
.page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #ffffff;
    min-height: 100vh;
}

.page-header {
    background: #ffffff;
    border-bottom: 2px solid #e5e7eb;
    padding: 24px 0;
    margin-bottom: 32px;
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    color: #111827;
    margin: 0 0 8px 0;
}

.page-subtitle {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
}

.breadcrumb {
    background: #f9fafb;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    border: 1px solid #e5e7eb;
}

.breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 32px;
    align-items: start;
}

.main-content {
    background: #ffffff;
}

.sidebar {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 24px;
    position: sticky;
    top: 20px;
}

.section {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 24px;
}

.section-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-content {
    padding: 24px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.info-item {
    margin-bottom: 16px;
}

.info-label {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 4px;
    font-weight: 500;
}

.info-value {
    color: #111827;
    font-weight: 500;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 500;
    gap: 6px;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-progress {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-completed {
    background-color: #d1fae5;
    color: #065f46;
}

.status-rejected {
    background-color: #fee2e2;
    color: #991b1b;
}

.btn {
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s;
}

.btn-primary {
    background-color: #3b82f6;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-success {
    background-color: #10b981;
    color: #ffffff;
}

.btn-success:hover {
    background-color: #059669;
}

.btn-secondary {
    background-color: #6b7280;
    color: #ffffff;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid #d1d5db;
    color: #374151;
}

.btn-outline:hover {
    background-color: #f9fafb;
}

.file-list {
    border-top: 1px solid #e5e7eb;
}

.file-item {
    padding: 16px 20px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-item:last-child {
    border-bottom: none;
}

.file-item:hover {
    background: #f9fafb;
}

.file-icon {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 14px;
    flex-shrink: 0;
}

.file-icon.pdf { background-color: #dc2626; }
.file-icon.image { background-color: #7c3aed; }
.file-icon.document { background-color: #2563eb; }
.file-icon.text { background-color: #6b7280; }
.file-icon.default { background-color: #6b7280; }

.file-info {
    flex: 1;
    min-width: 0;
    overflow: hidden;
}

.file-name {
    font-weight: 500;
    color: #111827;
    margin: 0 0 4px 0;
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    font-size: 14px;
    line-height: 1.4;
    max-width: 100%;
}

.file-meta {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #6b7280;
    flex-wrap: wrap;
    word-break: break-word;
    overflow-wrap: break-word;
}

.file-original-name {
    font-size: 11px;
    color: #6b7280;
    margin: 2px 0;
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.3;
}

.file-actions {
    display: flex;
    gap: 6px;
    flex-shrink: 0;
}

.timeline {
    position: relative;
}

.timeline-item {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.timeline-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 14px;
}

.timeline-content {
    flex: 1;
}

.timeline-title {
    font-weight: 600;
    color: #111827;
    margin: 0 0 4px 0;
    font-size: 14px;
}

.timeline-desc {
    color: #6b7280;
    font-size: 12px;
    margin: 0;
}

@media (max-width: 768px) {
    .content-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .file-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .page-container {
        padding: 16px;
    }
}
</style>
<!-- Professional Page Container -->
<div class="page-container">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb">
        <a href="/requests">جميع الطلبات</a> /
        <span>الطلب {{ req.request_number }}</span>
    </nav>

    <!-- Page Header -->
    <header class="page-header">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 20px;">
            <div style="flex: 1;">
                <h1 class="page-title">تفاصيل الطلب - الدفاع المدني</h1>
                <p class="page-subtitle">
                    رقم الطلب: <strong>{{ req.request_number }}</strong>
                </p>
            </div>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                {% if current_user.role.value == 'ADMIN' or req.user_id == current_user.id %}
                <a href="/requests/{{ req.id }}/edit" class="btn btn-primary">
                    <i class="fas fa-edit"></i>
                    تعديل الطلب
                </a>
                {% endif %}
                {% if (current_user.role.value == 'ADMIN') or (req.user_id == current_user.id) %}
                <a href="/requests/{{ req.id }}/files" class="btn btn-success">
                    <i class="fas fa-file-upload"></i>
                    إدارة الملفات
                </a>
                {% endif %}
                <a href="/requests" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للطلبات
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content Layout -->
    <div class="content-grid">
        <!-- Primary Content Area -->
        <div class="main-content">
            <!-- Request Overview -->
            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-info-circle" style="color: #3b82f6;"></i>
                        معلومات الطلب
                    </h2>
                </div>
                <div class="section-content">
                    <!-- Status Badge -->
                    <div style="margin-bottom: 24px;">
                        {% if req.status.value == 'PENDING' %}
                            <span class="status-badge status-pending">
                                <i class="fas fa-clock"></i>
                                قيد المراجعة
                            </span>
                        {% elif req.status.value == 'IN_PROGRESS' %}
                            <span class="status-badge status-progress">
                                <i class="fas fa-cog"></i>
                                قيد التنفيذ
                            </span>
                        {% elif req.status.value == 'COMPLETED' %}
                            <span class="status-badge status-completed">
                                <i class="fas fa-check-circle"></i>
                                مكتمل
                            </span>
                        {% elif req.status.value == 'REJECTED' %}
                            <span class="status-badge status-rejected">
                                <i class="fas fa-times-circle"></i>
                                مرفوض
                            </span>
                        {% endif %}
                    </div>

                    <!-- Request Information Grid -->
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">رقم الطلب</div>
                            <div class="info-value">{{ req.request_number }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">الرمز التعريفي</div>
                            <div class="info-value" style="display: flex; align-items: center; gap: 8px;">
                                <span>{{ req.unique_code }}</span>
                                <button onclick="copyToClipboard('{{ req.unique_code }}')" class="btn-outline" style="padding: 4px 8px; font-size: 12px;">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">تاريخ الإنشاء</div>
                            <div class="info-value">{{ req.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">آخر تحديث</div>
                            <div class="info-value">
                                {% if req.updated_at %}
                                    {{ req.updated_at.strftime('%Y-%m-%d %H:%M') }}
                                {% else %}
                                    لم يتم التحديث
                                {% endif %}
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">مقدم الطلب</div>
                            <div class="info-value">{{ req.user.full_name }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">البريد الإلكتروني</div>
                            <div class="info-value">{{ req.user.email }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Personal Information -->
            {% if req.full_name or req.personal_number or req.phone_number %}
            <div class="section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-user" style="color: #10b981;"></i>
                        المعلومات الشخصية
                    </h3>
                </div>
                <div class="section-content">
                    <div class="info-grid">
                        {% if req.full_name %}
                        <div class="info-item">
                            <div class="info-label">الاسم الثلاثي</div>
                            <div class="info-value">{{ req.full_name }}</div>
                        </div>
                        {% endif %}
                        {% if req.personal_number %}
                        <div class="info-item">
                            <div class="info-label">الرقم الشخصي</div>
                            <div class="info-value">{{ req.personal_number }}</div>
                        </div>
                        {% endif %}
                        {% if req.phone_number %}
                        <div class="info-item">
                            <div class="info-label">رقم الهاتف</div>
                            <div class="info-value">{{ req.phone_number }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Building Information -->
            {% if req.building_name or req.road_name or req.building_number or req.civil_defense_file_number or req.building_permit_number %}
            <div class="section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-building" style="color: #f59e0b;"></i>
                        معلومات المبنى
                    </h3>
                </div>
                <div class="section-content">
                    <div class="info-grid">
                        {% if req.building_name %}
                        <div class="info-item">
                            <div class="info-label">المبنى</div>
                            <div class="info-value">{{ req.building_name }}</div>
                        </div>
                        {% endif %}
                        {% if req.road_name %}
                        <div class="info-item">
                            <div class="info-label">الطريق</div>
                            <div class="info-value">{{ req.road_name }}</div>
                        </div>
                        {% endif %}
                        {% if req.building_number %}
                        <div class="info-item">
                            <div class="info-label">المجمع</div>
                            <div class="info-value">{{ req.building_number }}</div>
                        </div>
                        {% endif %}
                        {% if req.civil_defense_file_number %}
                        <div class="info-item">
                            <div class="info-label">رقم ملف الدفاع المدني</div>
                            <div class="info-value">{{ req.civil_defense_file_number }}</div>
                        </div>
                        {% endif %}
                        {% if req.building_permit_number %}
                        <div class="info-item">
                            <div class="info-label">رقم إجازة البناء</div>
                            <div class="info-value">{{ req.building_permit_number }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Service Sections -->
            {% set has_services = req.licenses_section or req.fire_equipment_section or req.commercial_records_section or req.engineering_offices_section or req.hazardous_materials_section %}
            {% if has_services %}
            <div class="section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-cogs" style="color: #7c3aed;"></i>
                        الأقسام المطلوبة
                    </h3>
                </div>
                <div class="section-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                        {% if req.licenses_section %}
                        <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: #f9fafb; border-radius: 6px;">
                            <div style="width: 32px; height: 32px; background: #dbeafe; border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-certificate" style="color: #2563eb; font-size: 14px;"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600; color: #111827; font-size: 14px;">قسم التراخيص</div>
                                <div style="font-size: 12px; color: #6b7280;">إصدار وتجديد التراخيص المختلفة</div>
                            </div>
                        </div>
                        {% endif %}
                        {% if req.fire_equipment_section %}
                        <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: #f9fafb; border-radius: 6px;">
                            <div style="width: 32px; height: 32px; background: #fee2e2; border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-fire-extinguisher" style="color: #dc2626; font-size: 14px;"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600; color: #111827; font-size: 14px;">قسم معدات الإطفاء</div>
                                <div style="font-size: 12px; color: #6b7280;">فحص وصيانة معدات الإطفاء والسلامة</div>
                            </div>
                        </div>
                        {% endif %}
                        {% if req.commercial_records_section %}
                        <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: #f9fafb; border-radius: 6px;">
                            <div style="width: 32px; height: 32px; background: #d1fae5; border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-file-contract" style="color: #10b981; font-size: 14px;"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600; color: #111827; font-size: 14px;">قسم السجلات التجارية</div>
                                <div style="font-size: 12px; color: #6b7280;">إدارة السجلات والوثائق التجارية</div>
                            </div>
                        </div>
                        {% endif %}
                        {% if req.engineering_offices_section %}
                        <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: #f9fafb; border-radius: 6px;">
                            <div style="width: 32px; height: 32px; background: #fef3c7; border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-drafting-compass" style="color: #f59e0b; font-size: 14px;"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600; color: #111827; font-size: 14px;">قسم المكاتب الهندسية</div>
                                <div style="font-size: 12px; color: #6b7280;">الاستشارات والخدمات الهندسية</div>
                            </div>
                        </div>
                        {% endif %}
                        {% if req.hazardous_materials_section %}
                        <div style="display: flex; align-items: center; gap: 12px; padding: 12px; background: #f9fafb; border-radius: 6px;">
                            <div style="width: 32px; height: 32px; background: #fed7aa; border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-exclamation-triangle" style="color: #ea580c; font-size: 14px;"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600; color: #111827; font-size: 14px;">قسم المواد الخطرة</div>
                                <div style="font-size: 12px; color: #6b7280;">التعامل مع المواد الخطرة والكيميائية</div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Additional Information -->
            {% if req.description %}
            <div class="section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-info-circle" style="color: #6366f1;"></i>
                        معلومات إضافية
                    </h3>
                </div>
                <div class="section-content">
                    <div class="info-grid">
                        <div class="info-item" style="grid-column: 1 / -1;">
                            <div class="info-label">وصف الطلب</div>
                            <div class="info-value" style="white-space: pre-wrap;">{{ req.description }}</div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Status Timeline -->
            <div style="margin-bottom: 24px;">
                <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-history" style="color: #3b82f6;"></i>
                    مراحل الطلب
                </h3>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-icon" style="background-color: #d1fae5; color: #065f46;">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">تم إنشاء الطلب</div>
                            <div class="timeline-desc">{{ req.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                        </div>
                    </div>

                    {% if req.status.value in ['IN_PROGRESS', 'COMPLETED'] %}
                    <div class="timeline-item">
                        <div class="timeline-icon" style="background-color: #dbeafe; color: #1e40af;">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">قيد التنفيذ</div>
                            <div class="timeline-desc">تم قبول الطلب وبدء التنفيذ</div>
                        </div>
                    </div>
                    {% endif %}

                    {% if req.status.value == 'COMPLETED' %}
                    <div class="timeline-item">
                        <div class="timeline-icon" style="background-color: #d1fae5; color: #065f46;">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">مكتمل</div>
                            <div class="timeline-desc">تم إنجاز الطلب بنجاح</div>
                        </div>
                    </div>
                    {% elif req.status.value == 'REJECTED' %}
                    <div class="timeline-item">
                        <div class="timeline-icon" style="background-color: #fee2e2; color: #991b1b;">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">مرفوض</div>
                            <div class="timeline-desc">تم رفض الطلب</div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Files Summary -->
            <div style="margin-bottom: 24px;">
                <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-paperclip" style="color: #10b981;"></i>
                    المرفقات ({{ req.files|length }})
                </h3>

                {% if req.files %}
                    {% set files_by_category = {} %}
                    {% for file in req.files %}
                        {% set category = file.file_category or 'general' %}
                        {% if category not in files_by_category %}
                            {% set _ = files_by_category.update({category: []}) %}
                        {% endif %}
                        {% set _ = files_by_category[category].append(file) %}
                    {% endfor %}

                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        {% for category, files in files_by_category.items() %}
                        <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 6px; padding: 12px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <div style="font-size: 14px; font-weight: 500; display: flex; align-items: center; gap: 6px;">
                                    {% if category == 'architectural_plans' %}
                                        <i class="fas fa-drafting-compass" style="color: #2563eb; font-size: 12px;"></i>
                                        <span>المخططات المعمارية</span>
                                    {% elif category == 'electrical_mechanical_plans' %}
                                        <i class="fas fa-bolt" style="color: #f59e0b; font-size: 12px;"></i>
                                        <span>المخططات الكهربائية</span>
                                    {% elif category == 'inspection_department' %}
                                        <i class="fas fa-search" style="color: #7c3aed; font-size: 12px;"></i>
                                        <span>قسم التفتيش</span>
                                    {% elif category == 'fire_equipment_files' %}
                                        <i class="fas fa-fire-extinguisher" style="color: #dc2626; font-size: 12px;"></i>
                                        <span>معدات الإطفاء</span>
                                    {% elif category == 'commercial_records_files' %}
                                        <i class="fas fa-file-contract" style="color: #10b981; font-size: 12px;"></i>
                                        <span>السجلات التجارية</span>
                                    {% elif category == 'engineering_offices_files' %}
                                        <i class="fas fa-building" style="color: #6366f1; font-size: 12px;"></i>
                                        <span>المكاتب الهندسية</span>
                                    {% elif category == 'hazardous_materials_files' %}
                                        <i class="fas fa-exclamation-triangle" style="color: #ea580c; font-size: 12px;"></i>
                                        <span>المواد الخطرة</span>
                                    {% elif category == 'licenses_files' %}
                                        <i class="fas fa-certificate" style="color: #2563eb; font-size: 12px;"></i>
                                        <span>التراخيص</span>
                                    {% else %}
                                        <i class="fas fa-file" style="color: #6b7280; font-size: 12px;"></i>
                                        <span>ملفات عامة</span>
                                    {% endif %}
                                </div>
                                <span style="font-size: 12px; color: #6b7280;">{{ files|length }}</span>
                            </div>

                            <div class="file-list">
                                {% for file in files %}
                                <div class="file-item">
                                    <!-- File Icon -->
                                    <div class="file-icon
                                        {% if file.stored_filename.lower().endswith('.pdf') %}pdf
                                        {% elif file.stored_filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')) %}image
                                        {% elif file.stored_filename.lower().endswith(('.doc', '.docx')) %}document
                                        {% else %}default{% endif %}">
                                        {% if file.stored_filename.lower().endswith('.pdf') %}
                                            <i class="fas fa-file-pdf"></i>
                                        {% elif file.stored_filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')) %}
                                            <i class="fas fa-file-image"></i>
                                        {% elif file.stored_filename.lower().endswith(('.doc', '.docx')) %}
                                            <i class="fas fa-file-word"></i>
                                        {% else %}
                                            <i class="fas fa-file"></i>
                                        {% endif %}
                                    </div>

                                    <!-- File Info -->
                                    <div class="file-info">
                                        <div class="file-name">{{ file.stored_filename }}</div>
                                        {% if file.stored_filename != file.original_filename %}
                                        <div class="file-original-name">
                                            الاسم الأصلي: {{ file.original_filename }}
                                        </div>
                                        {% endif %}
                                        <div class="file-meta">
                                            <span>{{ file.file_type.upper() if file.file_type else 'غير محدد' }}</span>
                                            <span>{{ "%.1f"|format(file.file_size / 1024 / 1024) }} ميجابايت</span>
                                            <span>{{ file.uploaded_at.strftime('%m-%d %H:%M') if file.uploaded_at else 'غير محدد' }}</span>
                                        </div>
                                    </div>

                                    <!-- File Actions -->
                                    <div class="file-actions">
                                        {% if file.stored_filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')) %}
                                        <button onclick="previewImage('{{ file.stored_filename }}', '/files/view/{{ file.id }}')" class="btn btn-outline" style="padding: 4px 8px; font-size: 11px;"
                                                title="معاينة الصورة">
                                            <i class="fas fa-search-plus"></i>
                                        </button>
                                        {% endif %}
                                        <a href="/files/view/{{ file.id }}" target="_blank" class="btn btn-primary" style="padding: 4px 8px; font-size: 11px;" title="عرض الملف">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/files/download/{{ file.id }}" class="btn btn-success" style="padding: 4px 8px; font-size: 11px;" title="تحميل الملف">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div style="text-align: center; padding: 20px; color: #6b7280;">
                        <i class="fas fa-file-upload" style="font-size: 24px; margin-bottom: 8px; color: #9ca3af;"></i>
                        <div style="font-weight: 500; margin-bottom: 4px;">لا توجد مرفقات</div>
                        <div style="font-size: 14px;">لم يتم رفع أي ملفات مع هذا الطلب</div>
                    </div>
                {% endif %}
            </div>

            <!-- Quick Actions -->
            {% if (current_user.role.value == 'ADMIN') or (req.user_id == current_user.id) %}
            <div>
                <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-bolt" style="color: #f59e0b;"></i>
                    إجراءات سريعة
                </h3>
                <div style="display: flex; flex-direction: column; gap: 8px;">
                    <a href="/requests/{{ req.id }}/files" class="btn btn-primary" style="text-align: center; text-decoration: none;">
                        <i class="fas fa-file-upload"></i>
                        إدارة المرفقات
                    </a>
                    <a href="/requests/{{ req.id }}/edit" class="btn btn-success" style="text-align: center; text-decoration: none;">
                        <i class="fas fa-edit"></i>
                        تعديل الطلب
                    </a>
                    <a href="/requests" class="btn btn-secondary" style="text-align: center; text-decoration: none;">
                        <i class="fas fa-list"></i>
                        جميع الطلبات
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Image Preview Modal -->
<div id="imagePreviewModal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); display: none; z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: 8px; max-width: 90%; max-height: 90%; overflow: hidden;">
        <div style="padding: 20px; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center;">
            <h3 id="imagePreviewTitle" style="margin: 0; font-size: 18px; font-weight: 600;">معاينة الصورة</h3>
            <button onclick="closeImagePreview()" class="btn btn-outline" style="padding: 8px;">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div style="padding: 20px; max-height: 70vh; overflow: auto;">
            <img id="imagePreviewImg" src="" alt="" style="max-width: 100%; height: auto; border-radius: 6px;">
        </div>
        <div style="padding: 20px; border-top: 1px solid #e5e7eb; display: flex; justify-content: space-between;">
            <button onclick="closeImagePreview()" class="btn btn-secondary">
                <i class="fas fa-times"></i>
                إغلاق
            </button>
            <a id="imageDownloadLink" href="" download class="btn btn-primary">
                <i class="fas fa-download"></i>
                تحميل الصورة
            </a>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        toast.textContent = 'تم نسخ الرمز التعريفي';
        document.body.appendChild(toast);
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    });
}

// Image preview functionality
function previewImage(filename, imageUrl) {
    document.getElementById('imagePreviewTitle').textContent = filename;
    document.getElementById('imagePreviewImg').src = imageUrl;
    document.getElementById('imageDownloadLink').href = imageUrl.replace('/view/', '/download/');
    document.getElementById('imagePreviewModal').style.display = 'flex';
}

function closeImagePreview() {
    document.getElementById('imagePreviewModal').style.display = 'none';
}

// Close modal when clicking outside
document.getElementById('imagePreviewModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImagePreview();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImagePreview();
    }
});

// Enhanced file actions
function openFileInNewTab(fileId) {
    window.open(`/files/view/${fileId}`, '_blank');
}

function downloadFile(fileId, filename) {
    const link = document.createElement('a');
    link.href = `/files/download/${fileId}`;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}


</script>
{% endblock %}