{% extends "base.html" %}

{% block title %}{% if is_admin %}جميع الطلبات{% else %}طلباتي{% endif %} - CMSVS{% endblock %}

{% block content %}
<style>
/* Dropdown positioning fixes */
.table-dropdown {
    position: relative;
}

.table-dropdown .dropdown-menu {
    position: absolute;
    z-index: 1000;
    min-width: 14rem;
    max-width: 16rem;
}

/* Support for upward dropdown positioning */
.table-dropdown .dropdown-menu.mb-2 {
    bottom: 100%;
    top: auto;
    margin-bottom: 0.5rem;
    margin-top: 0;
}

.table-dropdown .dropdown-menu.mt-2 {
    top: 100%;
    bottom: auto;
    margin-top: 0.5rem;
    margin-bottom: 0;
}

/* Prevent horizontal scroll when dropdown is open */
.overflow-x-auto {
    overflow-x: auto;
    overflow-y: visible;
}

/* Ensure dropdowns don't cause horizontal scrolling */
.table-container {
    position: relative;
    overflow-x: auto;
    max-width: 100%;
}

/* Ensure table rows can contain positioned dropdowns */
.table tbody tr {
    position: relative;
}

/* Responsive table styling */
.table {
    min-width: 100%;
    width: max-content;
}

/* Responsive column widths */
.table th,
.table td {
    white-space: nowrap;
    padding: 0.75rem 0.5rem;
}

/* Specific column width controls */
.table th:first-child,
.table td:first-child {
    min-width: 120px; /* Request number */
}

.table th:nth-child(2),
.table td:nth-child(2) {
    min-width: 140px; /* User column (admin only) */
}

.table th:nth-child(3),
.table td:nth-child(3) {
    min-width: 160px; /* Unique code */
}

.table th:nth-child(4),
.table td:nth-child(4) {
    min-width: 150px; /* Full name */
}

.table th:nth-child(5),
.table td:nth-child(5) {
    min-width: 130px; /* Building permit */
}

.table th:nth-child(6),
.table td:nth-child(6) {
    min-width: 120px; /* Status */
}

.table th:nth-child(7),
.table td:nth-child(7) {
    min-width: 100px; /* Attachments */
}

.table th:nth-child(8),
.table td:nth-child(8) {
    min-width: 120px; /* Created date */
}

.table th:last-child,
.table td:last-child {
    min-width: 120px; /* Actions */
}

/* Mobile responsive dropdown */
@media (max-width: 640px) {
    .table-dropdown .dropdown-menu {
        min-width: 12rem;
        max-width: 14rem;
    }

    /* Reduce column widths on mobile */
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.875rem;
    }

    .table th:first-child,
    .table td:first-child {
        min-width: 100px;
    }

    .table th:nth-child(2),
    .table td:nth-child(2) {
        min-width: 120px;
    }

    .table th:nth-child(3),
    .table td:nth-child(3) {
        min-width: 140px;
    }

    .table th:nth-child(4),
    .table td:nth-child(4) {
        min-width: 120px;
    }

    .table th:nth-child(5),
    .table td:nth-child(5) {
        min-width: 110px;
    }

    .table th:nth-child(6),
    .table td:nth-child(6) {
        min-width: 100px;
    }

    .table th:nth-child(7),
    .table td:nth-child(7) {
        min-width: 80px;
    }

    .table th:nth-child(8),
    .table td:nth-child(8) {
        min-width: 100px;
    }

    .table th:last-child,
    .table td:last-child {
        min-width: 100px;
    }
}

/* Tablet responsive */
@media (max-width: 1024px) {
    .table th,
    .table td {
        padding: 0.625rem 0.375rem;
    }
}

/* Additional responsive fixes */
.card {
    max-width: 100%;
    overflow: hidden;
}

.card-body {
    max-width: 100%;
    overflow-x: auto;
}

/* Ensure no horizontal overflow */
body {
    overflow-x: hidden;
}

.space-y-6 > * {
    max-width: 100%;
}

/* Search section toggle animation */
#searchSectionContent {
    transition: opacity 0.2s ease-in-out;
    overflow: hidden;
}

#searchSectionContent.hidden {
    display: none !important;
}

#searchToggleIcon {
    transition: transform 0.3s ease-in-out;
}

/* Quick info section */
#searchQuickInfo {
    transition: all 0.2s ease-in-out;
}

/* Toggle button hover effects */
#searchToggleBtn:hover #searchToggleIcon {
    transform: scale(1.1);
}

#searchToggleBtn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Smooth border transitions */
.card-header {
    transition: border-color 0.2s ease-in-out;
}

/* Quick info badges */
.filter-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}






</style>

<div class="w-full max-w-full overflow-hidden">
<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        {% if is_admin %}
        <h4 class="text-3xl font-bold text-gray-900">جميع الطلبات</h4>
        <p class="text-gray-600 mt-1">إدارة ومراقبة جميع طلبات المستخدمين في النظام</p>
        {% else %}
        <h4 class="text-3xl font-bold text-gray-900">طلباتي</h4>
        <p class="text-gray-600 mt-1">إدارة وتتبع جميع طلباتك الشخصية</p>
        {% endif %}
    </div>

    <!-- Status Filter Tabs -->
    <div class="card">
        <div class="card-body">
            <div class="flex flex-wrap gap-2">
                <a href="/requests" class="btn-secondary {% if not current_status %}btn-primary{% endif %}">
                    الكل
                </a>
                {% for status in statuses %}
                <a href="/requests?status={{ status }}" class="btn-secondary {% if current_status == status %}btn-primary{% endif %}">
                    {% if status == 'pending' %}قيد المراجعة
                    {% elif status == 'in_progress' %}قيد التنفيذ
                    {% elif status == 'completed' %}مكتملة
                    {% elif status == 'rejected' %}مرفوضة
                    {% endif %}
                </a>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2 rtl:space-x-reverse">
                    <h5 class="text-lg font-semibold text-gray-900">البحث والتصفية</h5>
                    {% if current_search or current_status %}
                    <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                    {% endif %}
                </div>
                <button type="button" onclick="toggleSearchSection()" class="{% if current_search or current_status %}text-blue-600 hover:text-blue-700{% else %}text-gray-500 hover:text-gray-700{% endif %} focus:outline-none p-2 rounded-md hover:bg-gray-100 transition-colors" id="searchToggleBtn">
                    <svg class="w-5 h-5 transform transition-transform duration-200" id="searchToggleIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
            </div>
            <!-- Quick info when collapsed -->
            <div id="searchQuickInfo" class="mt-3 border-t border-gray-200 pt-3 hidden">
                {% if current_search or current_status %}
                <div class="flex flex-wrap gap-2 mb-2">
                    {% if current_search %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        {{ current_search }}
                    </span>
                    {% endif %}
                    {% if current_status %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        {% if current_status == 'pending' %}قيد المراجعة
                        {% elif current_status == 'in_progress' %}قيد التنفيذ
                        {% elif current_status == 'completed' %}مكتملة
                        {% elif current_status == 'rejected' %}مرفوضة
                        {% endif %}
                    </span>
                    {% endif %}
                </div>
                {% endif %}
                <div class="text-sm text-gray-600">
                    <div class="flex items-center justify-between">
                        <span>
                            {% if current_search or current_status %}
                            <span class="font-medium">{{ total_requests }}</span> نتيجة
                            {% else %}
                            <span class="font-medium">{{ total_requests }}</span> طلب إجمالي
                            {% endif %}
                        </span>
                        {% if total_pages > 1 %}
                        <span class="text-gray-500">
                            الصفحة {{ current_page }} من {{ total_pages }}
                        </span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        <div id="searchSectionContent" class="card-body">
            <!-- Quick Filter Buttons -->
            <div class="mb-6">
                <h6 class="text-sm font-medium text-gray-700 mb-3">تصفية سريعة</h6>
                <div class="flex flex-wrap gap-2">
                    <a href="/requests"
                       class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors {% if not current_status %}bg-blue-100 text-blue-800 border border-blue-200{% else %}bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200{% endif %}">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        جميع الطلبات
                        <span class="mr-2 px-2 py-0.5 bg-white bg-opacity-60 rounded-full text-xs">
                            {% if user_stats %}
                                {% if 'total_requests' in user_stats %}
                                    {{ user_stats.total_requests }}
                                {% elif 'total' in user_stats %}
                                    {{ user_stats.total }}
                                {% else %}
                                    {{ total_requests }}
                                {% endif %}
                            {% else %}
                                {{ total_requests }}
                            {% endif %}
                        </span>
                    </a>

                    {% for status in statuses %}
                    <a href="/requests?status={{ status }}{% if current_search %}&search={{ current_search }}{% endif %}"
                       class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors {% if current_status == status %}{% if status == 'pending' %}bg-yellow-100 text-yellow-800 border border-yellow-200{% elif status == 'in_progress' %}bg-blue-100 text-blue-800 border border-blue-200{% elif status == 'completed' %}bg-green-100 text-green-800 border border-green-200{% elif status == 'rejected' %}bg-red-100 text-red-800 border border-red-200{% endif %}{% else %}bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200{% endif %}">
                        {% if status == 'pending' %}
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        قيد المراجعة
                        {% elif status == 'in_progress' %}
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        قيد التنفيذ
                        {% elif status == 'completed' %}
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        مكتملة
                        {% elif status == 'rejected' %}
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        مرفوضة
                        {% endif %}
                        <span class="mr-2 px-2 py-0.5 bg-white bg-opacity-60 rounded-full text-xs">
                            {% if user_stats %}
                                {% if 'status_distribution' in user_stats %}
                                    {{ user_stats.status_distribution[status] if status in user_stats.status_distribution else 0 }}
                                {% else %}
                                    {{ user_stats[status] if status in user_stats else 0 }}
                                {% endif %}
                            {% else %}
                                0
                            {% endif %}
                        </span>
                    </a>
                    {% endfor %}
                </div>
            </div>

            <!-- Advanced Search Form -->
            <div class="border-t border-gray-200 pt-6">
                <h6 class="text-sm font-medium text-gray-700 mb-4">بحث متقدم</h6>
                <form method="get" id="advancedSearchForm" class="space-y-4">
                    <!-- Preserve current status filter -->
                    {% if current_status %}
                    <input type="hidden" name="status" value="{{ current_status }}">
                    {% endif %}

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                البحث النصي
                            </label>
                            <input type="text"
                                   id="search"
                                   name="search"
                                   value="{{ current_search or '' }}"
                                   placeholder="رقم الطلب، الرمز التعريفي، الاسم..."
                                   class="input">
                        </div>

                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                من تاريخ
                            </label>
                            <input type="date"
                                   id="date_from"
                                   name="date_from"
                                   value="{{ request.query_params.get('date_from', '') }}"
                                   class="input">
                        </div>

                        <div>
                            <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                إلى تاريخ
                            </label>
                            <input type="date"
                                   id="date_to"
                                   name="date_to"
                                   value="{{ request.query_params.get('date_to', '') }}"
                                   class="input">
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pt-4 border-t border-gray-100">
                        <div class="flex flex-wrap gap-2">
                            <button type="submit" class="btn-primary">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                تطبيق البحث
                            </button>
                            <a href="/requests" class="btn-secondary">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                إعادة تعيين
                            </a>
                        </div>

                        {% if current_search or current_status or request.query_params.get('date_from') or request.query_params.get('date_to') %}
                        <div class="text-sm text-gray-600">
                            <span class="font-medium">{{ total_requests }}</span> نتيجة للبحث الحالي
                        </div>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>

    {% if requests %}
    <!-- New Request Button -->
    <div class="flex justify-end mb-4">
        <a href="/requests/new" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            إنشاء طلب جديد
        </a>
    </div>

    <!-- Requests Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="text-lg font-semibold text-gray-900">قائمة الطلبات</h5>
        </div>
        <div class="card-body p-0">
            <!-- Desktop Table View -->
            <div class="hidden md:block table-container overflow-x-auto relative">
                <table class="table">
                    <thead class="table-header">
                        <tr>
                            <th class="table-header-cell">رقم الطلب</th>
                            {% if is_admin %}
                            <th class="table-header-cell">المستخدم</th>
                            {% endif %}
                            <th class="table-header-cell">الرمز التعريفي</th>
                            <th class="table-header-cell">الاسم الكامل</th>
                            <th class="table-header-cell">إجازة البناء</th>
                            <th class="table-header-cell">الحالة</th>
                            <th class="table-header-cell">المرفقات</th>
                            <th class="table-header-cell">تاريخ الإنشاء</th>
                            <th class="table-header-cell">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="table-body">
                        {% for req in requests %}
                        <tr>
                            <td class="table-cell">
                                <code class="text-xs bg-gray-100 px-2 py-1 rounded">{{ req.request_number }}</code>
                            </td>
                            {% if is_admin %}
                            <td class="table-cell">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-xs font-medium ml-3">
                                        {{ req.user.full_name[0] if req.user.full_name else 'م' }}
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ req.user.full_name }}</div>
                                        <div class="text-xs text-gray-500">{{ req.user.email }}</div>
                                    </div>
                                </div>
                            </td>
                            {% endif %}
                            <td class="table-cell">
                                <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                    <code class="text-xs bg-blue-100 px-2 py-1 rounded text-blue-800">{{ req.unique_code }}</code>
                                    <button onclick="copyToClipboard('{{ req.unique_code }}')" class="text-gray-400 hover:text-gray-600" title="نسخ الرمز">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                            <td class="table-cell">
                                <span class="font-medium">{{ req.full_name or req.request_name or 'غير محدد' }}</span>
                            </td>
                            <td class="table-cell">
                                {% if req.building_permit_number %}
                                <code class="text-xs bg-green-100 px-2 py-1 rounded text-green-800">{{ req.building_permit_number }}</code>
                                {% else %}
                                <span class="text-gray-400 text-sm">غير محدد</span>
                                {% endif %}
                            </td>
                            <td class="table-cell">
                                {% if req.status.value == 'pending' %}
                                <span class="badge-warning">قيد المراجعة</span>
                                {% elif req.status.value == 'in_progress' %}
                                <span class="badge-info">قيد التنفيذ</span>
                                {% elif req.status.value == 'completed' %}
                                <span class="badge-success">مكتمل</span>
                                {% elif req.status.value == 'rejected' %}
                                <span class="badge-danger">مرفوض</span>
                                {% endif %}
                            <td class="table-cell">
                                {% if req.files %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ req.files|length }} ملف
                                </span>
                                {% else %}
                                <span class="text-gray-400 text-sm">لا توجد مرفقات</span>
                                {% endif %}
                            </td>
                            <td class="table-cell">
                                <span class="text-sm text-gray-600">{{ req.created_at.strftime('%Y-%m-%d') }}</span>
                            </td>
                            <td class="table-cell">
                                <div class="table-dropdown">
                                    <button onclick="toggleDropdown({{ req.id }})" class="btn-secondary text-sm">
                                        الإجراءات
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div id="dropdown-{{ req.id }}" class="dropdown-menu hidden mt-2 bg-white rounded-md shadow-lg border border-gray-200">
                                        <a href="/requests/{{ req.id }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">عرض التفاصيل</a>
                                        {% if req.status.value in ['pending', 'in_progress'] %}
                                        <a href="/requests/{{ req.id }}/edit" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">تعديل</a>
                                        {% endif %}
                                        <a href="/requests/{{ req.id }}/files" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">إدارة الملفات</a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Mobile Card View -->
            <div class="md:hidden">
                {% for req in requests %}
                <div class="mobile-table-card border-r-4 border-r-{{ 'green' if req.status.value == 'approved' else 'yellow' if req.status.value == 'pending' else 'red' if req.status.value == 'rejected' else 'blue' }}-500">
                    <!-- Request Header -->
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <div class="mobile-title">{{ req.full_name or 'غير محدد' }}</div>
                            <code class="text-xs bg-gray-100 px-2 py-1 rounded">{{ req.request_number }}</code>
                        </div>
                        <div class="text-right">
                            {% if req.status.value == 'pending' %}
                            <span class="badge-warning">قيد المراجعة</span>
                            {% elif req.status.value == 'approved' %}
                            <span class="badge-success">مقبول</span>
                            {% elif req.status.value == 'rejected' %}
                            <span class="badge-danger">مرفوض</span>
                            {% elif req.status.value == 'in_progress' %}
                            <span class="badge-info">قيد التنفيذ</span>
                            {% else %}
                            <span class="badge-gray">{{ req.status.value }}</span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Request Details -->
                    <div class="space-y-2 mb-4">
                        {% if is_admin %}
                        <div class="mobile-table-row">
                            <span class="mobile-table-label">المستخدم:</span>
                            <span class="mobile-table-value">{{ req.user.full_name }}</span>
                        </div>
                        {% endif %}

                        <div class="mobile-table-row">
                            <span class="mobile-table-label">الرمز التعريفي:</span>
                            <span class="mobile-table-value">{{ req.unique_code or 'غير محدد' }}</span>
                        </div>

                        <div class="mobile-table-row">
                            <span class="mobile-table-label">إجازة البناء:</span>
                            <span class="mobile-table-value">{{ req.building_permit or 'غير محدد' }}</span>
                        </div>

                        <div class="mobile-table-row">
                            <span class="mobile-table-label">المرفقات:</span>
                            <span class="mobile-table-value">
                                {% set file_count = req.files|length %}
                                {% if file_count > 0 %}
                                <span class="text-blue-600">{{ file_count }} ملف</span>
                                {% else %}
                                <span class="text-gray-500">لا توجد مرفقات</span>
                                {% endif %}
                            </span>
                        </div>

                        <div class="mobile-table-row">
                            <span class="mobile-table-label">تاريخ الإنشاء:</span>
                            <span class="mobile-table-value">{{ req.created_at.strftime('%Y-%m-%d') }}</span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-wrap gap-2">
                        <a href="/requests/{{ req.id }}" class="btn-primary text-xs px-3 py-2">
                            <i class="fas fa-eye"></i>
                            عرض
                        </a>

                        {% if current_user.id == req.user_id or current_user.role.value == 'admin' %}
                        <a href="/requests/{{ req.id }}/edit" class="btn-secondary text-xs px-3 py-2">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </a>
                        {% endif %}

                        {% if req.files %}
                        <a href="/requests/{{ req.id }}/files" class="btn-outline text-xs px-3 py-2">
                            <i class="fas fa-paperclip"></i>
                            المرفقات
                        </a>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% else %}
    <!-- No Requests Message -->
    <div class="card">
        <div class="card-body text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">لا توجد طلبات</h3>
            <p class="mt-1 text-sm text-gray-500">ابدأ بإنشاء طلب جديد.</p>
            <div class="mt-6">
                <a href="/requests/new" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    إنشاء طلب جديد
                </a>
            </div>
        </div>
    </div>
    {% endif %}




    <!-- JavaScript -->
    <script>
        // Toggle search section
        function toggleSearchSection() {
            const content = document.getElementById('searchSectionContent');
            const icon = document.getElementById('searchToggleIcon');
            const quickInfo = document.getElementById('searchQuickInfo');
            const toggleBtn = document.getElementById('searchToggleBtn');

            // Check if content is currently hidden
            const isHidden = content.classList.contains('hidden') ||
                           content.style.display === 'none' ||
                           getComputedStyle(content).display === 'none';

            if (isHidden) {
                // Show content (expand)
                content.classList.remove('hidden');
                content.style.display = 'block';
                icon.style.transform = 'rotate(180deg)';
                quickInfo.classList.add('hidden');
                toggleBtn.setAttribute('aria-expanded', 'true');

                // Add visual feedback
                content.style.opacity = '0';
                setTimeout(() => {
                    content.style.opacity = '1';
                }, 10);
            } else {
                // Hide content (collapse)
                content.style.opacity = '0';
                setTimeout(() => {
                    content.classList.add('hidden');
                    content.style.display = 'none';
                    quickInfo.classList.remove('hidden');
                }, 200);

                icon.style.transform = 'rotate(0deg)';
                toggleBtn.setAttribute('aria-expanded', 'false');
            }
        }

        // Toggle dropdown
        function toggleDropdown(requestId) {
            const dropdown = document.getElementById('dropdown-' + requestId);
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');

            // Close all other dropdowns
            allDropdowns.forEach(d => {
                if (d.id !== 'dropdown-' + requestId) {
                    d.classList.add('hidden');
                }
            });

            // Toggle current dropdown
            dropdown.classList.toggle('hidden');
        }

        // Copy to clipboard
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show success message
                const toast = document.createElement('div');
                toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50';
                toast.textContent = 'تم نسخ الرمز بنجاح';
                document.body.appendChild(toast);

                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 2000);
            });
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.table-dropdown')) {
                const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
                allDropdowns.forEach(d => d.classList.add('hidden'));
            }
        });

        // Initialize search section state
        document.addEventListener('DOMContentLoaded', function() {
            const hasFilters = {{ 'true' if current_search or current_status else 'false' }};
            const content = document.getElementById('searchSectionContent');
            const icon = document.getElementById('searchToggleIcon');
            const quickInfo = document.getElementById('searchQuickInfo');
            const toggleBtn = document.getElementById('searchToggleBtn');

            // Set initial state based on whether filters are active
            if (!hasFilters) {
                // Hide search section by default when no filters are active
                content.classList.add('hidden');
                content.style.display = 'none';
                content.style.opacity = '1';
                icon.style.transform = 'rotate(0deg)';
                quickInfo.classList.remove('hidden');
                toggleBtn.setAttribute('aria-expanded', 'false');
            } else {
                // Show search section when filters are active
                content.classList.remove('hidden');
                content.style.display = 'block';
                content.style.opacity = '1';
                icon.style.transform = 'rotate(180deg)';
                quickInfo.classList.add('hidden');
                toggleBtn.setAttribute('aria-expanded', 'true');
            }

            // Add accessibility attributes
            toggleBtn.setAttribute('aria-controls', 'searchSectionContent');
            toggleBtn.setAttribute('aria-label', 'تبديل قسم البحث والتصفية');

            // Enhanced search functionality
            const searchInput = document.getElementById('search');
            const dateFromInput = document.getElementById('date_from');
            const dateToInput = document.getElementById('date_to');
            const advancedForm = document.getElementById('advancedSearchForm');

            // Auto-submit on search input (with debounce)
            let searchTimeout;
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    const searchValue = this.value.trim();

                    searchTimeout = setTimeout(() => {
                        if (searchValue.length >= 3 || searchValue.length === 0) {
                            advancedForm.submit();
                        }
                    }, 800); // Increased delay for better UX
                });
            }

            // Auto-submit on date changes
            if (dateFromInput) {
                dateFromInput.addEventListener('change', function() {
                    advancedForm.submit();
                });
            }

            if (dateToInput) {
                dateToInput.addEventListener('change', function() {
                    advancedForm.submit();
                });
            }

            // Validate date range
            function validateDateRange() {
                if (dateFromInput && dateToInput && dateFromInput.value && dateToInput.value) {
                    const fromDate = new Date(dateFromInput.value);
                    const toDate = new Date(dateToInput.value);

                    if (fromDate > toDate) {
                        dateToInput.setCustomValidity('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
                        return false;
                    } else {
                        dateToInput.setCustomValidity('');
                        return true;
                    }
                }
                return true;
            }

            // Add date validation
            if (dateFromInput) {
                dateFromInput.addEventListener('change', validateDateRange);
            }
            if (dateToInput) {
                dateToInput.addEventListener('change', validateDateRange);
            }
        });
    </script>

</div>
</div>
{% endblock %}