{% extends "base.html" %}

{% block title %}الملف الشخصي - {{ profile_user.full_name or profile_user.username }}{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header Section -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">الملف الشخصي</h1>
            <p class="text-gray-600 mt-1">عرض وتحديث المعلومات الشخصية</p>
        </div>
        <div class="flex items-center space-x-4 rtl:space-x-reverse">
            <div class="text-right">
                <h6 class="text-sm font-medium text-gray-900">{{ profile_user.full_name or profile_user.username }}</h6>
                <small class="text-xs text-gray-500">{{ profile_user.role.value }}</small>
            </div>
        </div>
    </div>

    <!-- Profile Information Card -->
    <div class="card">
        <div class="card-body">
            <div class="flex items-center space-x-6 rtl:space-x-reverse mb-6">
                <!-- Avatar -->
                <div class="relative">
                    <div class="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                        {% if profile_user_avatar_url %}
                        <img src="{{ profile_user_avatar_url }}" alt="صورة المستخدم" class="w-full h-full object-cover">
                        {% else %}
                        <i class="fas fa-user text-3xl text-gray-400"></i>
                        {% endif %}
                    </div>
                    {% if current_user.role.value in ['admin', 'manager'] or current_user.id == profile_user.id %}
                    <button type="button" onclick="document.getElementById('avatarInput').click()"
                            class="absolute bottom-0 right-0 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center hover:bg-primary-700 transition-colors">
                        <i class="fas fa-camera text-xs"></i>
                    </button>
                    {% endif %}
                </div>

                <!-- User Info -->
                <div class="flex-1">
                    <h2 class="text-2xl font-bold text-gray-900">{{ profile_user.full_name or profile_user.username }}</h2>
                    <p class="text-gray-600">{{ profile_user.email }}</p>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 mt-2">
                        {{ profile_user.role.value }}
                    </span>
                </div>

                <!-- Actions -->
                <div class="flex space-x-3 rtl:space-x-reverse">
                    {% if current_user.role.value in ['admin', 'manager'] or current_user.id == profile_user.id %}
                    <button type="button" onclick="toggleEditForm()" class="btn-primary">
                        <i class="fas fa-edit"></i>
                        تحديث المعلومات
                    </button>
                    {% endif %}
                    <a href="/requests" class="btn-secondary">
                        <i class="fas fa-file-alt"></i>
                        الطلبات
                    </a>
                </div>
            </div>

            <!-- User Details Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                    <div class="p-3 md:p-3 bg-gray-50 rounded-lg border text-base md:text-sm">{{ profile_user.full_name or 'غير محدد' }}</div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">اسم المستخدم</label>
                    <div class="p-3 bg-gray-50 rounded-lg border">{{ profile_user.username }}</div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                    <div class="p-3 bg-gray-50 rounded-lg border">{{ profile_user.email }}</div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">الدور</label>
                    <div class="p-3 bg-gray-50 rounded-lg border">{{ profile_user.role.value }}</div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ التسجيل</label>
                    <div class="p-3 bg-gray-50 rounded-lg border">{{ profile_user.created_at.strftime('%Y-%m-%d') if profile_user.created_at else 'غير محدد' }}</div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                    <div class="p-3 bg-gray-50 rounded-lg border">
                        {% if profile_user.is_active %}
                        <span class="text-green-600"><i class="fas fa-check-circle"></i> نشط</span>
                        {% else %}
                        <span class="text-red-600"><i class="fas fa-times-circle"></i> غير نشط</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Profile Form (Hidden by default) -->
    <div id="editForm" class="card hidden">
        <div class="card-body">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">تحديث المعلومات الشخصية</h3>
                <button type="button" onclick="toggleEditForm()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            {% if current_user.role.value in ['admin', 'manager'] or current_user.id == profile_user.id %}
            <form method="post" action="/profile/update" enctype="multipart/form-data">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                    <div>
                        <label for="full_name" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                        <input type="text" id="full_name" name="full_name"
                               value="{{ profile_user.full_name or '' }}"
                               class="form-input">
                    </div>

                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                        <input type="text" id="username" name="username"
                               value="{{ profile_user.username }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email"
                               value="{{ profile_user.email }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الجديدة (اختياري)</label>
                        <input type="password" id="password" name="password"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               placeholder="اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور">
                    </div>
                </div>

                <div class="flex justify-end space-x-3 rtl:space-x-reverse mt-6">
                    <button type="button" onclick="toggleEditForm()" class="btn-secondary">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </form>
            {% endif %}
        </div>
    </div>

    <!-- Avatar Upload Form (Hidden) -->
    <form id="avatarForm" method="post" action="/avatar/upload/{{ profile_user.id }}" enctype="multipart/form-data" style="display: none;">
        <input type="file" id="avatarInput" name="avatar" accept="image/*" onchange="uploadAvatar()">
    </form>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-6">
        <div class="card">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-file-alt text-primary-600"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">طلباتي</h3>
                <p class="text-sm text-gray-600 mb-4">عرض وإدارة جميع الطلبات</p>
                <a href="/requests" class="btn-primary">
                    عرض الطلبات
                </a>
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-plus text-success-600"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">طلب جديد</h3>
                <p class="text-sm text-gray-600 mb-4">إنشاء طلب جديد</p>
                <a href="/requests/new" class="btn-success">
                    طلب جديد
                </a>
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <div class="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-tachometer-alt text-warning-600"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">لوحة التحكم</h3>
                <p class="text-sm text-gray-600 mb-4">العودة للوحة الرئيسية</p>
                <a href="/dashboard" class="btn-warning">
                    لوحة التحكم
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function toggleEditForm() {
    const editForm = document.getElementById('editForm');
    editForm.classList.toggle('hidden');
}

function uploadAvatar() {
    const form = document.getElementById('avatarForm');
    const fileInput = document.getElementById('avatarInput');

    if (fileInput.files.length > 0) {
        // Show loading state
        const avatarButton = document.querySelector('.fa-camera').parentElement;
        avatarButton.innerHTML = '<i class="fas fa-spinner fa-spin text-xs"></i>';

        // Submit form
        form.submit();
    }
}

// Show alerts based on URL parameters
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.get('avatar_updated')) {
        showAlert('تم تحديث الصورة الشخصية بنجاح', 'success');
    }
    if (urlParams.get('avatar_deleted')) {
        showAlert('تم حذف الصورة الشخصية بنجاح', 'success');
    }
    if (urlParams.get('profile_updated')) {
        showAlert('تم تحديث الملف الشخصي بنجاح', 'success');
    }
    if (urlParams.get('error')) {
        showAlert('حدث خطأ أثناء العملية', 'error');
    }
});

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} mb-4`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        ${message}
    `;

    // Insert at the top of the content
    const content = document.querySelector('.space-y-6');
    content.insertBefore(alertDiv, content.firstChild);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}




