{% extends "base.html" %}

{% block title %}إدارة ملفات الطلب {{ req.request_number }} - CMSVS{% endblock %}

{% block content %}
<style>
/* Professional File Management Styling */
.page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #ffffff;
    min-height: 100vh;
}

.page-header {
    background: #ffffff;
    border-bottom: 2px solid #e5e7eb;
    padding: 24px 0;
    margin-bottom: 32px;
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    color: #111827;
    margin: 0 0 8px 0;
}

.page-subtitle {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
}

.breadcrumb {
    background: #f9fafb;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    border: 1px solid #e5e7eb;
}

.breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 32px;
    align-items: start;
}

.main-content {
    background: #ffffff;
}

.sidebar {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 24px;
    position: sticky;
    top: 20px;
}

.section {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 24px;
}

.section-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-content {
    padding: 24px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    text-align: center;
    padding: 16px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
}

.file-list {
    border-top: 1px solid #e5e7eb;
}

.file-item {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 16px;
}

.file-item:last-child {
    border-bottom: none;
}

.file-item:hover {
    background: #f9fafb;
}

.file-checkbox {
    width: 16px;
    height: 16px;
}

.file-icon {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 16px;
    flex-shrink: 0;
}

.file-icon.pdf { background-color: #dc2626; }
.file-icon.image { background-color: #7c3aed; }
.file-icon.document { background-color: #2563eb; }
.file-icon.text { background-color: #6b7280; }
.file-icon.default { background-color: #6b7280; }

.file-info {
    flex: 1;
    min-width: 0;
    overflow: hidden;
}

.file-name {
    font-weight: 600;
    color: #111827;
    margin: 0 0 4px 0;
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.4;
    max-width: 100%;
}

.file-meta {
    display: flex;
    gap: 16px;
    font-size: 14px;
    color: #6b7280;
    flex-wrap: wrap;
    word-break: break-word;
    overflow-wrap: break-word;
}

.file-original-name {
    font-size: 12px;
    color: #6b7280;
    margin: 4px 0;
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.3;
}

.file-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s;
}

.btn-primary {
    background-color: #3b82f6;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-success {
    background-color: #10b981;
    color: #ffffff;
}

.btn-success:hover {
    background-color: #059669;
}

.btn-danger {
    background-color: #ef4444;
    color: #ffffff;
}

.btn-danger:hover {
    background-color: #dc2626;
}

.btn-secondary {
    background-color: #6b7280;
    color: #ffffff;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid #d1d5db;
    color: #374151;
}

.btn-outline:hover {
    background-color: #f9fafb;
}

.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    background: #f9fafb;
}

.upload-area:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.upload-icon {
    width: 48px;
    height: 48px;
    background-color: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    color: #ffffff;
    font-size: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.alert {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    border: 1px solid;
}

.alert-success {
    background-color: #ecfdf5;
    border-color: #10b981;
    color: #065f46;
}

.alert-error {
    background-color: #fef2f2;
    border-color: #ef4444;
    color: #991b1b;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-icon {
    width: 64px;
    height: 64px;
    background-color: #f3f4f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 24px;
}

@media (max-width: 768px) {
    .content-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .file-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .page-container {
        padding: 16px;
    }
}
</style>


<!-- Professional Page Container -->
<div class="page-container">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb">
        <a href="/requests">جميع الطلبات</a> /
        <a href="/requests/{{ req.id }}">الطلب {{ req.request_number }}</a> /
        <span>إدارة الملفات</span>
    </nav>

    <!-- Page Header -->
    <header class="page-header">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 20px;">
            <div style="flex: 1;">
                <h1 class="page-title">إدارة ملفات الطلب</h1>
                <p class="page-subtitle">
                    رقم الطلب: <strong>{{ req.request_number }}</strong> |
                    العنوان: <strong>{{ req.request_title or 'غير محدد' }}</strong>
                    {% if req.files %} | الملفات: <strong>{{ req.files|length }}</strong>{% endif %}
                </p>
            </div>
            <div style="display: flex; gap: 12px;">
                <a href="/requests/{{ req.id }}/edit" class="btn btn-success">
                    <i class="fas fa-edit"></i>
                    تعديل الطلب
                </a>
                <a href="/requests/{{ req.id }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للطلب
                </a>
            </div>
        </div>
    </header>

    <!-- Success/Error Messages -->
    {% if success %}
    <div class="alert alert-success">
        <strong>تم بنجاح:</strong> {{ success }}
        {% if upload_warnings %}
        <div style="margin-top: 12px;">
            <strong>تحذيرات:</strong>
            <ul style="margin: 8px 0 0 20px;">
                {% for warning in upload_warnings %}
                <li>{{ warning }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}
    </div>
    {% endif %}

    {% if error %}
    <div class="alert alert-error">
        <strong>حدث خطأ:</strong> {{ error }}
        {% if upload_errors %}
        <div style="margin-top: 12px;">
            <strong>تفاصيل الأخطاء:</strong>
            <ul style="margin: 8px 0 0 20px;">
                {% for error_detail in upload_errors %}
                <li>{{ error_detail }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Main Content Layout -->
    <div class="content-grid">
        <!-- Primary Content Area -->
        <div class="main-content">
            <!-- File Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ req.files|length }}</div>
                    <div class="stat-label">إجمالي الملفات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">
                        {% set total_size = 0 %}
                        {% for file in req.files %}
                            {% set total_size = total_size + (file.file_size or 0) %}
                        {% endfor %}
                        {{ "%.1f"|format(total_size / 1024 / 1024) }}
                    </div>
                    <div class="stat-label">ميجابايت</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">
                        {% set file_types = req.files|map(attribute='file_type')|unique|list %}
                        {{ file_types|length }}
                    </div>
                    <div class="stat-label">أنواع الملفات</div>
                </div>
            </div>

            <!-- Current Files Section -->
            <div class="section">
                <div class="section-header">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h2 class="section-title">
                            <i class="fas fa-folder-open"></i>
                            الملفات المرفقة ({{ req.files|length }})
                        </h2>
                        {% if req.files %}
                        <div style="display: flex; gap: 12px; align-items: center;">
                            <button onclick="toggleSelectAll()" id="selectAllBtn" class="btn btn-outline">
                                تحديد الكل
                            </button>
                            <button onclick="downloadSelected()" id="downloadSelectedBtn" class="btn btn-success" style="display: none;">
                                <i class="fas fa-download"></i>
                                تحميل المحدد
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>

                {% if req.files %}
                <div class="file-list">
                    {% for file in req.files %}
                    <div class="file-item" data-file-id="{{ file.id }}">
                        <!-- File Selection Checkbox -->
                        <input type="checkbox" class="file-checkbox" value="{{ file.id }}" onchange="updateSelectedCount()">

                        <!-- File Icon -->
                        <div class="file-icon
                            {% if file.file_type.lower() == 'pdf' %}pdf
                            {% elif file.file_type.lower() in ['jpg', 'jpeg', 'png', 'gif'] %}image
                            {% elif file.file_type.lower() in ['doc', 'docx'] %}document
                            {% elif file.file_type.lower() == 'txt' %}text
                            {% else %}default{% endif %}">
                            {% if file.file_type.lower() == 'pdf' %}
                                <i class="fas fa-file-pdf"></i>
                            {% elif file.file_type.lower() in ['jpg', 'jpeg', 'png', 'gif'] %}
                                <i class="fas fa-file-image"></i>
                            {% elif file.file_type.lower() in ['doc', 'docx'] %}
                                <i class="fas fa-file-word"></i>
                            {% elif file.file_type.lower() == 'txt' %}
                                <i class="fas fa-file-alt"></i>
                            {% else %}
                                <i class="fas fa-file"></i>
                            {% endif %}
                        </div>

                        <!-- File Info -->
                        <div class="file-info">
                            <h3 class="file-name">{{ file.stored_filename }}</h3>
                            {% if file.stored_filename != file.original_filename %}
                            <p class="file-original-name">
                                الاسم الأصلي: {{ file.original_filename }}
                            </p>
                            {% endif %}
                            <div class="file-meta">
                                <span>{{ file.file_type.upper() }}</span>
                                <span>{{ (file.file_size / 1024 / 1024)|round(2) }} ميجابايت</span>
                                <span>{{ file.uploaded_at.strftime('%Y-%m-%d %H:%M') if file.uploaded_at else 'غير محدد' }}</span>
                                {% if file.file_category %}
                                <span>{{ file.file_category }}</span>
                                {% endif %}
                            </div>
                        </div>

                        <!-- File Actions -->
                        <div class="file-actions">
                            {% if file.stored_filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')) %}
                            <button onclick="previewImage('{{ file.stored_filename }}', '/files/view/{{ file.id }}')" class="btn btn-outline" title="معاينة الصورة">
                                <i class="fas fa-search-plus"></i>
                                معاينة
                            </button>
                            {% endif %}
                            {% if file.file_path %}
                            <a href="/files/view/{{ file.id }}" target="_blank" class="btn btn-primary" title="عرض الملف">
                                <i class="fas fa-eye"></i>
                                عرض
                            </a>
                            {% endif %}
                            <a href="/files/download/{{ file.id }}" class="btn btn-success" title="تحميل الملف">
                                <i class="fas fa-download"></i>
                                تحميل
                            </a>
                            <form method="post" action="/requests/{{ req.id }}/files/{{ file.id }}/delete" style="display: inline;">
                                <button type="submit" onclick="return confirm('هل أنت متأكد من حذف هذا الملف؟\n\nسيتم حذف الملف نهائياً ولا يمكن استرداده.')" class="btn btn-danger" title="حذف الملف">
                                    <i class="fas fa-trash"></i>
                                    حذف
                                </button>
                            </form>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 8px;">لا توجد ملفات مرفقة</h3>
                    <p>لم يتم إرفاق أي ملفات بهذا الطلب حتى الآن. يمكنك إضافة ملفات جديدة أدناه.</p>
                </div>
                {% endif %}
            </div>

            <!-- Add Files Section -->
            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-plus-circle"></i>
                        إضافة ملفات جديدة
                    </h2>
                </div>
                <div class="section-content">
                    <form method="post" action="/requests/{{ req.id }}/files/add" enctype="multipart/form-data" id="addFilesForm">
                        <!-- File Category Selection -->
                        <div class="form-group">
                            <label for="file_category" class="form-label">
                                فئة الملفات <span style="color: #ef4444;">*</span>
                            </label>
                            <select id="file_category" name="file_category" class="form-control">
                                <option value="additional_documents">مستندات إضافية</option>
                                <option value="architectural_plans">المخططات المعمارية</option>
                                <option value="electrical_mechanical_plans">المخططات الكهربائية والميكانيكية</option>
                                <option value="inspection_department">قسم التفتيش</option>
                                <option value="fire_equipment_files">ملفات معدات مقاومة الحريق</option>
                                <option value="commercial_records_files">ملفات السجلات التجارية</option>
                                <option value="engineering_offices_files">ملفات المكاتب الهندسية</option>
                                <option value="hazardous_materials_files">ملفات المواد الخطرة</option>
                            </select>
                        </div>

                        <!-- File Upload Area -->
                        <div class="form-group">
                            <label for="files" class="form-label">
                                اختر الملفات <span style="color: #ef4444;">*</span>
                            </label>
                            <div class="upload-area" id="dropZone">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">اختر الملفات أو اسحبها هنا</h3>
                                <p style="margin: 0 0 16px 0; color: #6b7280;">PDF, DOC, DOCX, TXT, JPG, JPEG, PNG, GIF - حتى 10 ميجابايت</p>
                                <label for="files" class="btn btn-primary" style="cursor: pointer;">
                                    <i class="fas fa-folder-open"></i>
                                    اختر الملفات
                                    <input id="files" name="files" type="file" multiple style="display: none;" accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif">
                                </label>
                            </div>
                        </div>

                        <div id="file-preview" style="margin-top: 16px;"></div>

                        <!-- Form Actions -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 24px; gap: 12px;">
                            <button type="button" onclick="document.getElementById('files').value=''; document.getElementById('file-preview').innerHTML='';" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                مسح الاختيار
                            </button>
                            <button type="submit" id="addFilesBtn" class="btn btn-success">
                                <i class="fas fa-upload"></i>
                                إضافة الملفات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Request Summary -->
            <div style="margin-bottom: 24px;">
                <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-info-circle" style="color: #3b82f6;"></i>
                    ملخص الطلب
                </h3>
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    <div>
                        <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">رقم الطلب</div>
                        <div style="font-weight: 600;">{{ req.request_number }}</div>
                    </div>
                    <div>
                        <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">عنوان الطلب</div>
                        <div>{{ req.request_title or 'غير محدد' }}</div>
                    </div>
                    <div>
                        <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">الحالة</div>
                        <div>
                            {% if req.status.value == 'PENDING' %}
                                <span style="display: inline-flex; align-items: center; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; background-color: #fef3c7; color: #92400e; gap: 4px;">
                                    <i class="fas fa-clock"></i>
                                    قيد المراجعة
                                </span>
                            {% elif req.status.value == 'IN_PROGRESS' %}
                                <span style="display: inline-flex; align-items: center; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; background-color: #dbeafe; color: #1e40af; gap: 4px;">
                                    <i class="fas fa-cog"></i>
                                    قيد التنفيذ
                                </span>
                            {% elif req.status.value == 'COMPLETED' %}
                                <span style="display: inline-flex; align-items: center; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; background-color: #d1fae5; color: #065f46; gap: 4px;">
                                    <i class="fas fa-check-circle"></i>
                                    مكتمل
                                </span>
                            {% elif req.status.value == 'REJECTED' %}
                                <span style="display: inline-flex; align-items: center; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; background-color: #fee2e2; color: #991b1b; gap: 4px;">
                                    <i class="fas fa-times-circle"></i>
                                    مرفوض
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    <div>
                        <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">مقدم الطلب</div>
                        <div>{{ req.user.full_name }}</div>
                    </div>
                    <div>
                        <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">تاريخ الإنشاء</div>
                        <div>{{ req.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                    </div>
                </div>
            </div>

            <!-- File Guidelines -->
            <div style="margin-bottom: 24px;">
                <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-file-alt" style="color: #10b981;"></i>
                    إرشادات الملفات
                </h3>
                <div style="display: flex; flex-direction: column; gap: 8px; font-size: 14px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-check" style="color: #10b981; font-size: 12px;"></i>
                        <span>عدد الملفات: غير محدود</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-check" style="color: #10b981; font-size: 12px;"></i>
                        <span>حجم الملف: حتى 10 ميجابايت</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-check" style="color: #10b981; font-size: 12px;"></i>
                        <span>الأنواع: PDF, DOC, DOCX, TXT, JPG, JPEG, PNG, GIF</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-check" style="color: #10b981; font-size: 12px;"></i>
                        <span>يمكن حذف وإضافة الملفات</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div>
                <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-bolt" style="color: #f59e0b;"></i>
                    إجراءات سريعة
                </h3>
                <div style="display: flex; flex-direction: column; gap: 8px;">
                    <a href="/requests/{{ req.id }}" class="btn btn-primary" style="text-align: center; text-decoration: none;">
                        <i class="fas fa-eye"></i>
                        عرض الطلب
                    </a>
                    <a href="/requests/{{ req.id }}/edit" class="btn btn-success" style="text-align: center; text-decoration: none;">
                        <i class="fas fa-edit"></i>
                        تعديل الطلب
                    </a>
                    <a href="/requests" class="btn btn-secondary" style="text-align: center; text-decoration: none;">
                        <i class="fas fa-list"></i>
                        جميع الطلبات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Preview Modal -->
<div id="imagePreviewModal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); display: none; z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: 8px; max-width: 90%; max-height: 90%; overflow: hidden;">
        <div style="padding: 20px; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center;">
            <h3 id="imagePreviewTitle" style="margin: 0; font-size: 18px; font-weight: 600;">معاينة الصورة</h3>
            <button onclick="closeImagePreview()" class="btn btn-outline" style="padding: 8px;">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div style="padding: 20px; max-height: 70vh; overflow: auto;">
            <img id="imagePreviewImg" src="" alt="" style="max-width: 100%; height: auto; border-radius: 6px;">
        </div>
        <div style="padding: 20px; border-top: 1px solid #e5e7eb; display: flex; justify-content: space-between;">
            <button onclick="closeImagePreview()" class="btn btn-secondary">
                <i class="fas fa-times"></i>
                إغلاق
            </button>
            <a id="imageDownloadLink" href="" download class="btn btn-primary">
                <i class="fas fa-download"></i>
                تحميل الصورة
            </a>
        </div>
    </div>
</div>

<script>
// Professional File Management JavaScript

// Bulk selection functionality
function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('.file-checkbox');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
    });

    selectAllBtn.textContent = allChecked ? 'تحديد الكل' : 'إلغاء التحديد';
    updateSelectedCount();
}

function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.file-checkbox:checked');
    const downloadBtn = document.getElementById('downloadSelectedBtn');
    const selectAllBtn = document.getElementById('selectAllBtn');

    if (selectedCheckboxes.length > 0) {
        downloadBtn.style.display = 'inline-flex';
        downloadBtn.innerHTML = `<i class="fas fa-download"></i> تحميل المحدد (${selectedCheckboxes.length})`;
    } else {
        downloadBtn.style.display = 'none';
    }

    const allCheckboxes = document.querySelectorAll('.file-checkbox');
    const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
    selectAllBtn.textContent = allChecked ? 'إلغاء التحديد' : 'تحديد الكل';
}

function downloadSelected() {
    const selectedCheckboxes = document.querySelectorAll('.file-checkbox:checked');
    const fileIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (fileIds.length === 0) {
        alert('يرجى تحديد ملف واحد على الأقل');
        return;
    }

    // Create multiple download links
    fileIds.forEach(fileId => {
        const link = document.createElement('a');
        link.href = `/files/download/${fileId}`;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
}

// Image preview functionality
function previewImage(filename, imageUrl) {
    document.getElementById('imagePreviewTitle').textContent = filename;
    document.getElementById('imagePreviewImg').src = imageUrl;
    document.getElementById('imageDownloadLink').href = imageUrl.replace('/view/', '/download/');
    document.getElementById('imagePreviewModal').style.display = 'flex';
}

function closeImagePreview() {
    document.getElementById('imagePreviewModal').style.display = 'none';
}

// Close modal when clicking outside
document.getElementById('imagePreviewModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImagePreview();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImagePreview();
    }
});

// File preview functionality
document.getElementById('files').addEventListener('change', function(e) {
    const preview = document.getElementById('file-preview');
    preview.innerHTML = '';

    if (e.target.files.length > 0) {
        const fileList = document.createElement('div');
        fileList.style.cssText = 'background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 6px; padding: 16px; margin-top: 16px;';

        const title = document.createElement('h4');
        title.style.cssText = 'font-weight: 600; margin: 0 0 12px 0; font-size: 14px;';
        title.textContent = 'الملفات المحددة:';
        fileList.appendChild(title);

        Array.from(e.target.files).forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.style.cssText = 'display: flex; align-items: center; gap: 12px; padding: 8px 0; border-bottom: 1px solid #e5e7eb;';

            const icon = document.createElement('i');
            if (file.type.includes('pdf')) {
                icon.className = 'fas fa-file-pdf';
                icon.style.color = '#dc2626';
            } else if (file.type.includes('image')) {
                icon.className = 'fas fa-file-image';
                icon.style.color = '#7c3aed';
            } else if (file.type.includes('word')) {
                icon.className = 'fas fa-file-word';
                icon.style.color = '#2563eb';
            } else {
                icon.className = 'fas fa-file';
                icon.style.color = '#6b7280';
            }

            const fileDetails = document.createElement('div');
            fileDetails.style.cssText = 'flex: 1;';

            const fileName = document.createElement('div');
            fileName.style.cssText = 'font-weight: 500; font-size: 14px; margin-bottom: 2px;';
            fileName.textContent = file.name;

            const fileSize = document.createElement('div');
            fileSize.style.cssText = 'font-size: 12px; color: #6b7280;';
            fileSize.textContent = `${(file.size / 1024 / 1024).toFixed(2)} ميجابايت`;

            fileDetails.appendChild(fileName);
            fileDetails.appendChild(fileSize);
            fileItem.appendChild(icon);
            fileItem.appendChild(fileDetails);
            fileList.appendChild(fileItem);
        });

        preview.appendChild(fileList);
    }
});

// Drag and drop functionality
const dropZone = document.getElementById('dropZone');
if (dropZone) {
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        dropZone.style.borderColor = '#3b82f6';
        dropZone.style.backgroundColor = '#eff6ff';
    }

    function unhighlight(e) {
        dropZone.style.borderColor = '#d1d5db';
        dropZone.style.backgroundColor = '#f9fafb';
    }

    dropZone.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        document.getElementById('files').files = files;
        document.getElementById('files').dispatchEvent(new Event('change'));
    }
}

// Form submission with loading state
document.getElementById('addFilesForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('addFilesBtn');
    const filesInput = document.getElementById('files');

    if (!filesInput.files || filesInput.files.length === 0) {
        e.preventDefault();
        alert('يرجى اختيار ملف واحد على الأقل');
        return;
    }

    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';
    submitBtn.disabled = true;

    // Re-enable button after 30 seconds as fallback
    setTimeout(() => {
        submitBtn.innerHTML = '<i class="fas fa-upload"></i> إضافة الملفات';
        submitBtn.disabled = false;
    }, 30000);
});
</script>
{% endblock %}