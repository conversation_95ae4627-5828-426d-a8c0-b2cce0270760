@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts for Arabic support */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* Base RTL styles */
@layer base {
  html {
    direction: rtl;
    font-family: 'Noto Sans Arabic', Tahoma, Arial, sans-serif;
  }
  
  body {
    @apply text-gray-900 bg-gray-50;
    font-feature-settings: "kern" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Ensure proper RTL text alignment */
  [dir="rtl"] {
    text-align: right;
  }
  
  /* Fix for HTMX indicators in RTL */
  .htmx-indicator {
    @apply opacity-0 transition-opacity;
  }
  
  .htmx-request .htmx-indicator {
    @apply opacity-100;
  }
  
  .htmx-request.htmx-indicator {
    @apply opacity-100;
  }
}

/* Custom component styles */
@layer components {
  /* Mobile-First Button styles */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-3 text-base font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 touch-target;
    @apply sm:px-4 sm:py-2 sm:text-sm sm:rounded-md;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }

  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }

  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }

  .btn-outline {
    @apply btn border-2 border-primary-600 text-primary-600 bg-transparent hover:bg-primary-600 hover:text-white focus:ring-primary-500;
  }
  
  /* Mobile-First Card styles */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
    @apply mx-4 sm:mx-0;
  }

  .card-header {
    @apply px-4 py-3 border-b border-gray-200;
    @apply sm:px-6 sm:py-4;
  }

  .card-body {
    @apply px-4 py-3;
    @apply sm:px-6 sm:py-4;
  }

  .card-footer {
    @apply px-4 py-3 border-t border-gray-200 bg-gray-50;
    @apply sm:px-6 sm:py-4;
  }
  
  /* Mobile-First Form styles */
  .form-group {
    @apply mb-4;
    @apply sm:mb-4;
  }

  .form-label {
    @apply block text-base font-medium text-gray-700 mb-2;
    @apply sm:text-sm;
  }

  .form-input {
    @apply block w-full px-4 py-3 text-base border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 touch-target;
    @apply sm:px-3 sm:py-2 sm:text-sm sm:rounded-md;
  }

  .form-textarea {
    @apply form-input resize-vertical min-h-[120px];
    @apply sm:min-h-[100px];
  }

  .form-select {
    @apply form-input appearance-none bg-white;
  }

  .form-control {
    @apply form-input;
  }
  
  /* Mobile-First Alert styles */
  .alert {
    @apply p-4 rounded-lg mb-4 text-base;
    @apply sm:rounded-md sm:text-sm;
  }

  .alert-success {
    @apply alert bg-success-50 text-success-800 border border-success-200;
  }

  .alert-warning {
    @apply alert bg-warning-50 text-warning-800 border border-warning-200;
  }

  .alert-danger {
    @apply alert bg-danger-50 text-danger-800 border border-danger-200;
  }

  .alert-info {
    @apply alert bg-primary-50 text-primary-800 border border-primary-200;
  }
  
  /* Navigation styles */
  .nav-link {
    @apply block px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors;
  }
  
  .nav-link-active {
    @apply nav-link bg-primary-100 text-primary-700;
  }
  
  /* Table styles */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  /* Status badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }
  
  .badge-info {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-gray {
    @apply badge bg-gray-100 text-gray-800;
  }
}

/* Mobile-First Responsive Utilities */
@layer utilities {
  /* RTL utilities */
  .rtl\:text-right {
    text-align: right;
  }

  .rtl\:text-left {
    text-align: left;
  }

  .rtl\:float-right {
    float: right;
  }

  .rtl\:float-left {
    float: left;
  }

  .rtl\:mr-auto {
    margin-right: auto;
  }

  .rtl\:ml-auto {
    margin-left: auto;
  }

  /* Mobile-specific utilities */
  .mobile-container {
    @apply px-4 mx-auto max-w-full;
  }

  .mobile-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 mb-4;
  }

  .mobile-card-header {
    @apply px-4 py-3 border-b border-gray-200 bg-gray-50;
  }

  .mobile-card-body {
    @apply px-4 py-3;
  }

  .mobile-button {
    @apply w-full sm:w-auto px-4 py-3 text-base font-medium rounded-lg transition-colors;
  }

  /* Responsive Grid Utilities */
  .responsive-grid {
    @apply grid grid-cols-1 gap-4;
    @apply sm:grid-cols-2 sm:gap-6;
    @apply lg:grid-cols-3 lg:gap-8;
    @apply xl:grid-cols-4;
  }

  .responsive-stats-grid {
    @apply grid grid-cols-1 gap-4;
    @apply sm:grid-cols-2 sm:gap-6;
    @apply lg:grid-cols-4 lg:gap-6;
  }

  /* Mobile-First Table Styles */
  .mobile-table {
    @apply block sm:table w-full;
  }

  .mobile-table thead {
    @apply hidden sm:table-header-group;
  }

  .mobile-table tbody {
    @apply block sm:table-row-group;
  }

  .mobile-table tr {
    @apply block sm:table-row border border-gray-200 rounded-lg mb-4 p-4;
    @apply sm:border-0 sm:rounded-none sm:mb-0 sm:p-0;
  }

  .mobile-table td {
    @apply block sm:table-cell text-right sm:text-center border-0 py-2;
    @apply sm:border-b sm:border-gray-200 sm:py-3 sm:px-4;
  }

  .mobile-table td:before {
    content: attr(data-label) ": ";
    @apply font-semibold text-gray-700 inline-block w-24 sm:hidden;
  }

  /* Mobile-First Form Styles */
  .mobile-form {
    @apply space-y-4 sm:space-y-6;
  }

  .mobile-form-group {
    @apply space-y-2;
  }

  .mobile-form-row {
    @apply grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6;
  }

  .mobile-form-actions {
    @apply flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-4 sm:justify-end;
  }

  /* Mobile-First Navigation */
  .mobile-nav-container {
    @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-30;
    @apply sm:hidden;
  }

  .mobile-nav-grid {
    @apply grid grid-cols-4 gap-1;
  }

  .mobile-nav-item {
    @apply flex flex-col items-center justify-center py-2 px-1 text-xs text-gray-600;
    @apply hover:text-blue-600 hover:bg-blue-50 transition-colors;
  }

  .mobile-nav-item.active {
    @apply text-blue-600 bg-blue-50;
  }

  /* Mobile-First Content Spacing */
  .mobile-content {
    @apply px-4 py-6 sm:px-6 sm:py-8 lg:px-8;
  }

  .mobile-section {
    @apply mb-6 sm:mb-8 lg:mb-10;
  }

  .mobile-header {
    @apply mb-4 sm:mb-6;
  }

  /* Touch-Friendly Elements */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  .touch-button {
    @apply px-6 py-3 text-base font-medium rounded-lg transition-colors touch-target;
    @apply sm:px-4 sm:py-2 sm:text-sm;
  }

  .mobile-input {
    @apply w-full px-4 py-3 text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }

  .mobile-nav-item {
    @apply block px-4 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors;
  }

  /* Touch-friendly sizing */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Mobile table alternatives */
  .mobile-table-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-3;
  }

  .mobile-table-row {
    @apply flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0;
  }

  .mobile-table-label {
    @apply text-sm font-medium text-gray-600;
  }

  .mobile-table-value {
    @apply text-sm text-gray-900 text-right;
  }

  /* Mobile typography */
  .mobile-title {
    @apply text-xl font-bold text-gray-900 mb-2;
  }

  .mobile-subtitle {
    @apply text-base text-gray-600 mb-4;
  }

  /* Mobile spacing */
  .mobile-section {
    @apply mb-6;
  }

  .mobile-section-gap {
    @apply mb-8;
  }

  /* Mobile-first typography utilities */
  .heading-1 {
    @apply text-2xl font-bold text-gray-900 mb-4;
    @apply sm:text-3xl sm:mb-6;
  }

  .heading-2 {
    @apply text-xl font-semibold text-gray-900 mb-3;
    @apply sm:text-2xl sm:mb-4;
  }

  .heading-3 {
    @apply text-lg font-medium text-gray-900 mb-2;
    @apply sm:text-xl sm:mb-3;
  }

  .body-text {
    @apply text-base text-gray-700 leading-relaxed;
    @apply sm:text-sm;
  }

  .small-text {
    @apply text-sm text-gray-600;
    @apply sm:text-xs;
  }

  /* Mobile-first spacing utilities */
  .section-padding {
    @apply py-6 px-4;
    @apply sm:py-8 sm:px-6;
  }

  .content-spacing {
    @apply space-y-4;
    @apply sm:space-y-6;
  }

  .button-spacing {
    @apply space-y-3;
    @apply sm:space-y-0 sm:space-x-3 sm:flex;
  }
}
